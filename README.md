# Swiss Budget Pro 🇨🇭

The premier financial planning tool designed specifically for Swiss residents pursuing Financial Independence and Early Retirement (FIRE). Swiss Budget Pro is the only comprehensive platform that combines Swiss tax optimization, real-time economic data, and professional-grade risk analysis.

## 🇨🇭 Built for Switzerland

Swiss Budget Pro understands the Swiss financial landscape like no other tool:

- **Complete 26-Canton Tax Engine**: Professional-grade tax calculations with 2024 rates
- **Advanced Pillar 3a Optimization**: Multi-account withdrawal strategies and tax-efficient planning
- **Real-time SNB & SIX Integration**: Live Swiss National Bank and SIX Swiss Exchange data
- **Professional Monte Carlo Analysis**: Institutional-grade risk assessment and stress testing
- **Three-Pillar System Integration**: Complete AHV/AVS, BVG/LPP, and 3a/3b planning

## ✨ Key Features

### 🔒 Bank-Level Security & Privacy Protection

- **AES-256-GCM Encryption**: Military-grade encryption for all user data
- **Zero-Knowledge Architecture**: Your data never leaves your device unencrypted
- **Swiss Privacy Compliance**: Full GDPR and Swiss Data Protection Act compliance
- **Granular Privacy Controls**: Complete control over your personal and financial data
- **Security Monitoring**: Real-time threat detection and comprehensive audit trail
- **Data Sovereignty**: You own and control 100% of your financial information

### 💰 Swiss Tax Optimization Engine

- **All 26 Swiss Cantons**: Accurate tax calculations for every canton
- **Pillar 3a Optimization**: Maximize tax-deferred savings with withdrawal strategies
- **Cantonal Comparison**: Find the most tax-efficient canton for your situation
- **Wealth Tax Integration**: Strategic planning for high net worth individuals
- **Annual Savings**: CHF 5,000-20,000+ potential tax savings per user

### 📈 Real-Time Economic Data Integration

- **Swiss National Bank**: Live policy rates, inflation data, economic indicators
- **SIX Swiss Exchange**: Real-time SMI/SPI data, bond yields, market volatility
- **Dynamic Return Modeling**: Automatic adjustment based on current economic conditions
- **Economic Alerts**: Real-time notifications for policy changes and market events

### 🧠 Advanced Analytics & Risk Assessment

- **Monte Carlo Simulations**: Run thousands of scenarios to test plan robustness
- **Stress Testing**: Bear markets, recessions, stagflation, and crisis scenarios
- **Safe Withdrawal Analysis**: Optimize retirement income with sequence of returns risk
- **Success Probability**: Statistical confidence in your FIRE plan
- **Risk Metrics**: Value at Risk, probability of ruin, expected shortfall

### 💾 Professional Data Management & Security

- **Encrypted Auto-Save**: Never lose your financial plans with AES-256-GCM encrypted 30-second auto-save
- **Secure Historical Tracking**: Monitor progress over time with encrypted trend analysis
- **Protected Scenario Management**: Save and compare multiple financial strategies with bank-level security
- **Encrypted Export/Import**: Professional data portability with secure encrypted backups
- **Privacy Dashboard**: Complete control over data retention, sharing, and deletion
- **Audit Trail**: Comprehensive security event logging for compliance and peace of mind

## 🚀 Quick Start with DevContainer (Recommended)

This project uses **VS Code DevContainers** for a consistent development environment.

### Prerequisites

- [Docker Desktop](https://www.docker.com/products/docker-desktop/)
- [VS Code](https://code.visualstudio.com/)
- [Dev Containers extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.remote-containers)

### Get Started

```bash
# Clone the repository
git clone <repository-url>
cd swiss-budget-pro

# Open in VS Code
code .

# When prompted, click "Reopen in Container"
# Or use Ctrl+Shift+P → "Dev Containers: Reopen in Container"
```

The container will automatically:

- ✅ Set up Node.js 20 environment
- ✅ Install all dependencies
- ✅ Configure development tools
- ✅ Set up testing framework
- ✅ Configure VS Code extensions

### Development Commands

```bash
npm run dev           # Start development server
npm test              # Run all tests
npm run test:watch    # Run tests in watch mode
npm run test:ui       # Visual test runner
npm run build         # Build for production
```

## 🏗️ Project Structure

```
swiss-budget-pro/
├── .devcontainer/          # Development container configuration
│   ├── devcontainer.json   # Main container config
│   ├── Dockerfile          # Custom container setup
│   └── setup.sh           # Post-creation script
├── .vscode/               # VS Code workspace settings
├── docs/                  # Comprehensive documentation
│   ├── architecture/      # System architecture documentation
│   ├── user-guide/        # User guides and tutorials
│   └── index.md          # Documentation home
├── test/                  # Comprehensive test suite
│   ├── components/        # Component tests
│   ├── integration/       # Integration tests
│   ├── fixtures/          # Test data and scenarios
│   └── utils/            # Test utilities
├── retire.tsx            # Main application (single-file architecture)
├── package.json          # Dependencies and scripts
├── vite.config.ts        # Build configuration
└── README.md            # This file
```

## 🏛️ Architecture Overview

Swiss Budget Pro features a sophisticated, modular architecture designed specifically for Swiss financial planning:

### 🔄 Data-Driven Architecture

- **Reactive Updates**: Real-time calculations as data changes
- **Swiss-First Design**: Built for Swiss regulations and tax laws
- **Component-Based**: Modular React components for maintainability

### 🧮 Core Calculation Engines

- **🧠 Monte Carlo Engine**: Advanced risk analysis with thousands of simulations
- **🇨🇭 Swiss Tax Engine**: Complete 26-canton tax optimization
- **📊 Economic Data Engine**: Real-time SNB and SIX market integration
- **🎯 FIRE Calculator**: Financial independence tracking and optimization
- **🔒 Security Engine**: Bank-level encryption and privacy protection

### 💾 Advanced Data Management

- **Encrypted Auto-Save**: Never lose your financial plans with AES-256-GCM protection
- **Secure Historical Tracking**: Monitor progress over time with encrypted storage
- **Protected Multiple Scenarios**: Compare different strategies with bank-level security
- **Encrypted Export/Import**: Professional data portability with secure backups
- **Privacy Controls**: Granular data management and retention policies
- **Security Monitoring**: Real-time threat detection and audit trail

For detailed architecture documentation, see [Architecture Overview](docs/architecture/index.md).

## 🧪 Testing Framework

Comprehensive testing setup with **Vitest + React Testing Library**:

### Test Categories

- **Unit Tests**: Financial calculations, utility functions, security modules
- **Component Tests**: React component behavior and rendering, security dashboard
- **Integration Tests**: localStorage, user workflows, encrypted data persistence
- **Hook Tests**: Custom React hooks
- **Security Tests**: Encryption, privacy controls, audit trail, threat detection
- **E2E Tests**: Complete security workflow and user data protection

### Test Commands

```bash
npm test                  # Run all tests
npm run test:watch        # Watch mode for development
npm run test:ui          # Visual test runner with browser UI
npm run test:coverage    # Generate coverage report
./scripts/test.sh        # Custom test runner with options
```

### Test Scenarios

- **Young Professional** (Age 25, starting career)
- **Mid-Career Family** (Age 40, family expenses)
- **Near Retirement** (Age 58, wealth preservation)
- **High Earner** (Aggressive FIRE strategy)
- **Edge Cases** (Zero income, corrupted data, etc.)

## 💾 Encrypted localStorage Implementation

Advanced localStorage functionality with bank-level security and robust fallbacks:

### Security Features

- ✅ **AES-256-GCM encryption** for all stored data
- ✅ **Zero-knowledge architecture** with local-only processing
- ✅ **Automatic persistence** of all user data with encryption
- ✅ **Debounced saves** (500ms) for performance with security
- ✅ **Graceful fallback** when localStorage unavailable
- ✅ **Error handling** for quota exceeded and corrupted data
- ✅ **Complex object serialization** for expenses and savings with encryption
- ✅ **Privacy controls** for data retention and deletion
- ✅ **Audit trail** for all data access and modifications

### Data Persisted (All Encrypted)

- User preferences (dark mode, active tab, privacy settings)
- Financial inputs (income, expenses, savings) - AES-256-GCM encrypted
- Configuration (ages, rates, targets) - Securely stored
- Dynamic arrays (expenses, savings goals) - Encrypted serialization
- Security settings (privacy controls, audit preferences)
- Encryption keys (securely derived, never stored in plain text)

## 🎯 Key Features

### Financial Planning

- **Income Management**: Primary employment, company income, additional sources
- **Budget Tracking**: Essential vs discretionary expenses
- **Savings Goals**: Emergency fund, Pillar 3a, investment portfolio
- **FIRE Calculations**: Financial independence tracking

### Swiss-Specific Features

- **Pillar 3a Integration**: Swiss retirement savings limits (CHF 7,056)
- **Swiss Pension System**: Pension contribution calculations
- **CHF Currency**: Proper Swiss franc formatting
- **Tax Optimization**: Swiss tax-efficient savings strategies

### Interactive Visualizations

- **Budget Donut Chart**: Expense allocation with D3.js
- **Projection Line Chart**: Long-term financial trajectory
- **Multiple Data Series**: Total balance, pension, real balance

### User Experience

- **Dark/Light Mode**: Persistent theme preference
- **Responsive Design**: Works on desktop and mobile
- **Real-time Updates**: Instant calculation updates
- **Data Export**: PDF report generation

## 🛠️ Development Environment

### DevContainer Benefits

- **Consistent Environment**: Same Node.js version across all machines
- **Zero Setup**: Everything pre-configured and ready to use
- **Isolated Development**: No system pollution or conflicts
- **Team Collaboration**: No "works on my machine" issues

### Pre-installed Tools

- **Node.js 20** (Latest LTS)
- **TypeScript** with full type checking
- **Vitest** for fast testing
- **ESLint + Prettier** for code quality
- **VS Code Extensions** for optimal development experience

### Available Aliases

```bash
dev          # npm run dev
build        # npm run build
test         # npm test
test-watch   # npm run test:watch
gs           # git status
ga           # git add
gc           # git commit
```

## 📊 Technical Stack

### Frontend

- **React 18** with TypeScript
- **Vite** for fast development and building
- **Tailwind CSS** for styling
- **D3.js** for data visualizations

### Testing

- **Vitest** as test runner
- **React Testing Library** for component testing
- **jsdom** for DOM simulation
- **@testing-library/user-event** for user interaction testing

### Development

- **TypeScript** for type safety
- **ESLint** for code linting
- **Prettier** for code formatting
- **VS Code DevContainer** for consistent environment

## 🚀 Getting Started (Alternative Methods)

### Local Development (Without Container)

```bash
# Requires Node.js 18+ and npm
npm install
npm run dev
```

### Production Build

```bash
npm run build
npm run preview
```

## 📖 Documentation

- **[Architecture Overview](docs/architecture/index.md)** - Complete system architecture and design
- **[Data Flow Architecture](docs/architecture/data-flow.md)** - How data flows through the system
- **[Component Architecture](docs/architecture/components.md)** - React component structure and organization
- **[DevContainer Setup](DEVCONTAINER_SETUP.md)** - Detailed container setup guide
- **[Test Documentation](test/README.md)** - Comprehensive testing guide
- **[Nix Setup](NIX_SETUP.md)** - Alternative Nix environment setup

## 🤝 Contributing

1. **Use the DevContainer** for consistent development environment
2. **Write tests** for new features and bug fixes
3. **Follow TypeScript** best practices
4. **Run tests** before committing (`npm test`)
5. **Use conventional commits** for clear history

### Development Workflow

```bash
# Start development
npm run dev

# Run tests during development
npm run test:watch

# Before committing
npm test
npm run build
```

## 📄 License

MIT License - see LICENSE file for details.

## 🎯 Project Goals

- **Swiss-focused**: Tailored for Swiss financial planning needs
- **User-friendly**: Intuitive interface for complex financial calculations
- **Accurate**: Precise calculations following Swiss financial regulations
- **Reliable**: Comprehensive testing and error handling
- **Maintainable**: Clean code with extensive documentation

---

**Happy financial planning! 🎉**
